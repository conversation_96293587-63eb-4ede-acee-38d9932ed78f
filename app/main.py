#!/usr/bin/env python3
"""
QA Agent Main Entry Point

This script can run the QA Agent in different modes:
- system: Run as a system application with hotkey listening
- tray: Run with system tray interface
- api: Run as a FastAPI web service (original mode)
"""

import sys
import argparse
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def run_system_app():
    """Run as system application"""
    from app.system_app import main
    main()


def run_tray_app():
    """Run with system tray"""
    from app.tray_app import main
    main()


def run_api_app():
    """Run as FastAPI application with frontend"""
    from fastapi import FastAPI, Request
    from fastapi.staticfiles import StaticFiles
    from fastapi.templating import Jinja2Templates
    from fastapi.responses import HTMLResponse
    import uvicorn
    import os

    app = FastAPI(title="DSA Assistant Agent", description="AI-powered DSA learning assistant")

    # Mount static files
    static_path = os.path.join(os.path.dirname(__file__), "static")
    if os.path.exists(static_path):
        app.mount("/static", StaticFiles(directory=static_path), name="static")

    # Setup templates
    templates_path = os.path.join(os.path.dirname(__file__), "templates")
    if os.path.exists(templates_path):
        templates = Jinja2Templates(directory=templates_path)
    else:
        templates = None

    @app.get("/", response_class=HTMLResponse)
    async def frontend(request: Request):
        """Serve the frontend interface"""
        if templates:
            return templates.TemplateResponse("index.html", {"request": request})
        else:
            return HTMLResponse("""
            <html>
                <body>
                    <h1>DSA Assistant Agent</h1>
                    <p>Frontend templates not found. API is available at <a href="/docs">/docs</a></p>
                </body>
            </html>
            """)

    @app.get("/health")
    async def health_check():
        return {"status": "ok", "service": "DSA Assistant Agent"}

    # Startup logging
    logger.info("DSA Assistant Agent starting...")
    logger.info("Frontend will be available at: http://localhost:8000")
    logger.info("API documentation at: http://localhost:8000/docs")

    # Add API endpoints
    from app.api import router
    app.include_router(router, prefix="/api/v1")

    uvicorn.run(app, host="0.0.0.0", port=8000)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="QA Agent Application")
    parser.add_argument(
        "mode",
        choices=["system", "tray", "api"],
        nargs="?",
        default="tray",
        help="Run mode: system (background service), tray (system tray), or api (web service)"
    )

    args = parser.parse_args()

    logger.info(f"Starting QA Agent in {args.mode} mode")

    try:
        if args.mode == "system":
            run_system_app()
        elif args.mode == "tray":
            run_tray_app()
        elif args.mode == "api":
            run_api_app()
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
