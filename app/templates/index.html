<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DSA Assistant Agent</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hotkey-card {
            transition: transform 0.2s;
            border-left: 4px solid #007bff;
        }
        .hotkey-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-fallback { background-color: #ffc107; }
        .response-area {
            background-color: #f8f9fa;
            border-radius: 8px;
            min-height: 200px;
            font-family: 'Courier New', monospace;
        }
        .navbar-brand {
            font-weight: bold;
            color: #007bff !important;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-brain"></i> DSA Assistant Agent
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text" id="status-indicator">
                    <span class="status-indicator status-offline"></span>
                    <span id="status-text">Checking status...</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Configuration Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> Configuration</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="geminiApiKey" class="form-label">Gemini API Key</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="geminiApiKey" 
                                               placeholder="Enter your Gemini API key">
                                        <button class="btn btn-outline-secondary" type="button" id="toggleApiKey">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        If not provided, the system will use ChatGPT web interface as fallback.
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="preferredLanguage" class="form-label">Preferred Language</label>
                                    <select class="form-select" id="preferredLanguage">
                                        <option value="python">Python</option>
                                        <option value="java">Java</option>
                                        <option value="cpp">C++</option>
                                        <option value="javascript">JavaScript</option>
                                        <option value="go">Go</option>
                                        <option value="rust">Rust</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="experienceLevel" class="form-label">Experience Level</label>
                                    <select class="form-select" id="experienceLevel">
                                        <option value="beginner">Beginner</option>
                                        <option value="intermediate">Intermediate</option>
                                        <option value="advanced">Advanced</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-primary" id="saveConfig">
                            <i class="fas fa-save"></i> Save Configuration
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hotkey Functions -->
        <div class="row mb-4">
            <div class="col-12">
                <h4><i class="fas fa-keyboard"></i> DSA Assistant Hotkeys</h4>
                <p class="text-muted">Use these hotkeys while working on DSA problems, or click the buttons below to test:</p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card hotkey-card h-100">
                    <div class="card-body">
                        <h6 class="card-title">
                            <span class="badge bg-primary">Ctrl+Shift+1</span>
                            Store Question
                        </h6>
                        <p class="card-text">Copy and store a DSA question with AI analysis</p>
                        <textarea class="form-control mb-2" id="questionInput" rows="3" 
                                  placeholder="Paste your DSA question here..."></textarea>
                        <button class="btn btn-outline-primary btn-sm" onclick="storeQuestion()">
                            <i class="fas fa-plus"></i> Store Question
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card hotkey-card h-100">
                    <div class="card-body">
                        <h6 class="card-title">
                            <span class="badge bg-success">Ctrl+Shift+2</span>
                            Get Hint
                        </h6>
                        <p class="card-text">Get progressive hints without spoiling the solution</p>
                        <select class="form-select mb-2" id="hintLevel">
                            <option value="approach">Approach Hint</option>
                            <option value="algorithm">Algorithm Hint</option>
                            <option value="implementation">Implementation Hint</option>
                            <option value="optimization">Optimization Hint</option>
                        </select>
                        <button class="btn btn-outline-success btn-sm" onclick="getHint()">
                            <i class="fas fa-lightbulb"></i> Get Hint
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card hotkey-card h-100">
                    <div class="card-body">
                        <h6 class="card-title">
                            <span class="badge bg-info">Ctrl+Shift+3</span>
                            Full Solution
                        </h6>
                        <p class="card-text">Get complete solution based on your history</p>
                        <button class="btn btn-outline-info btn-sm" onclick="getFullSolution()">
                            <i class="fas fa-code"></i> Get Solution
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card hotkey-card h-100">
                    <div class="card-body">
                        <h6 class="card-title">
                            <span class="badge bg-warning">Ctrl+Shift+4</span>
                            Interview Ready
                        </h6>
                        <p class="card-text">Get interview-ready solution progression</p>
                        <button class="btn btn-outline-warning btn-sm" onclick="getInterviewSolution()">
                            <i class="fas fa-user-tie"></i> Interview Solution
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card hotkey-card h-100">
                    <div class="card-body">
                        <h6 class="card-title">
                            <span class="badge bg-danger">Ctrl+Shift+5</span>
                            Code Review
                        </h6>
                        <p class="card-text">Review your code and get improvement suggestions</p>
                        <textarea class="form-control mb-2" id="codeInput" rows="3" 
                                  placeholder="Paste your code here..."></textarea>
                        <button class="btn btn-outline-danger btn-sm" onclick="reviewCode()">
                            <i class="fas fa-search"></i> Review Code
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Response Area -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-robot"></i> AI Response</h5>
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearResponse()">
                            <i class="fas fa-trash"></i> Clear
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="response-area p-3" id="responseArea">
                            <div class="text-muted text-center">
                                <i class="fas fa-comment-dots fa-2x mb-2"></i>
                                <p>AI responses will appear here...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> Usage Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="statsContainer">
                            <div class="col-md-3 text-center">
                                <h4 class="text-primary" id="totalQuestions">-</h4>
                                <small class="text-muted">Questions Analyzed</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 class="text-success" id="totalHints">-</h4>
                                <small class="text-muted">Hints Requested</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 class="text-info" id="totalSolutions">-</h4>
                                <small class="text-muted">Solutions Generated</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 class="text-warning" id="currentStreak">-</h4>
                                <small class="text-muted">Current Streak</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/app.js"></script>
</body>
</html>
