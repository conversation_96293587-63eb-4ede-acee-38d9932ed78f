"""
Language Detection and Code Analysis Utilities
"""

import re
import logging
from typing import Optional, Dict, List, Tuple
from enum import Enum

logger = logging.getLogger(__name__)


class ProgrammingLanguage(Enum):
    """Supported programming languages"""
    PYTHON = "python"
    JAVA = "java"
    CPP = "cpp"
    JAVASCRIPT = "javascript"
    GO = "go"
    RUST = "rust"
    C = "c"
    CSHARP = "csharp"
    UNKNOWN = "unknown"


class LanguageDetector:
    """Detects programming language from code snippets"""
    
    def __init__(self):
        # Language patterns for detection
        self.patterns = {
            ProgrammingLanguage.PYTHON: [
                r'\bdef\s+\w+\s*\(',
                r'\bclass\s+\w+\s*\(',
                r'\bimport\s+\w+',
                r'\bfrom\s+\w+\s+import',
                r':\s*$',  # Python's colon syntax
                r'\bprint\s*\(',
                r'\bif\s+.*:\s*$',
                r'\bfor\s+\w+\s+in\s+',
                r'\belif\s+.*:\s*$'
            ],
            ProgrammingLanguage.JAVA: [
                r'\bpublic\s+class\s+\w+',
                r'\bpublic\s+static\s+void\s+main',
                r'\bSystem\.out\.print',
                r'\bpublic\s+\w+\s+\w+\s*\(',
                r'\bprivate\s+\w+\s+\w+',
                r'\bimport\s+java\.',
                r'\@Override',
                r'\bnew\s+\w+\s*\(',
                r';\s*$'  # Semicolon endings
            ],
            ProgrammingLanguage.CPP: [
                r'#include\s*<.*>',
                r'\bstd::\w+',
                r'\busing\s+namespace\s+std',
                r'\bint\s+main\s*\(',
                r'\bcout\s*<<',
                r'\bcin\s*>>',
                r'\bvector\s*<.*>',
                r'\bclass\s+\w+\s*{',
                r';\s*$'  # Semicolon endings
            ],
            ProgrammingLanguage.JAVASCRIPT: [
                r'\bfunction\s+\w+\s*\(',
                r'\bconst\s+\w+\s*=',
                r'\blet\s+\w+\s*=',
                r'\bvar\s+\w+\s*=',
                r'\bconsole\.log\s*\(',
                r'=>\s*{',  # Arrow functions
                r'\brequire\s*\(',
                r'\bexport\s+',
                r'\bimport\s+.*\bfrom\b'
            ],
            ProgrammingLanguage.GO: [
                r'\bpackage\s+\w+',
                r'\bfunc\s+\w+\s*\(',
                r'\bimport\s+\(',
                r'\bfmt\.Print',
                r'\bvar\s+\w+\s+\w+',
                r':=\s*',  # Go's short variable declaration
                r'\bgo\s+\w+\s*\(',
                r'\bdefer\s+',
                r'\brange\s+'
            ],
            ProgrammingLanguage.RUST: [
                r'\bfn\s+\w+\s*\(',
                r'\blet\s+\w+\s*=',
                r'\blet\s+mut\s+\w+',
                r'\bprintln!\s*\(',
                r'\bmatch\s+\w+\s*{',
                r'\bimpl\s+\w+',
                r'\bstruct\s+\w+\s*{',
                r'\benum\s+\w+\s*{',
                r'&\w+'  # References
            ],
            ProgrammingLanguage.C: [
                r'#include\s*<.*\.h>',
                r'\bint\s+main\s*\(',
                r'\bprintf\s*\(',
                r'\bscanf\s*\(',
                r'\bmalloc\s*\(',
                r'\bfree\s*\(',
                r'\bstruct\s+\w+\s*{',
                r';\s*$'  # Semicolon endings
            ],
            ProgrammingLanguage.CSHARP: [
                r'\busing\s+System',
                r'\bpublic\s+class\s+\w+',
                r'\bstatic\s+void\s+Main',
                r'\bConsole\.Write',
                r'\bnamespace\s+\w+',
                r'\bpublic\s+\w+\s+\w+\s*\(',
                r'\bprivate\s+\w+\s+\w+',
                r';\s*$'  # Semicolon endings
            ]
        }
        
        # Language-specific keywords for additional scoring
        self.keywords = {
            ProgrammingLanguage.PYTHON: ['def', 'class', 'import', 'from', 'if', 'elif', 'else', 'for', 'while', 'try', 'except', 'with', 'as', 'lambda', 'yield'],
            ProgrammingLanguage.JAVA: ['public', 'private', 'protected', 'class', 'interface', 'extends', 'implements', 'static', 'final', 'abstract', 'synchronized'],
            ProgrammingLanguage.CPP: ['include', 'namespace', 'using', 'class', 'struct', 'template', 'typename', 'virtual', 'override', 'const', 'static'],
            ProgrammingLanguage.JAVASCRIPT: ['function', 'const', 'let', 'var', 'async', 'await', 'promise', 'callback', 'prototype', 'this'],
            ProgrammingLanguage.GO: ['package', 'import', 'func', 'var', 'const', 'type', 'struct', 'interface', 'go', 'defer', 'range', 'chan'],
            ProgrammingLanguage.RUST: ['fn', 'let', 'mut', 'struct', 'enum', 'impl', 'trait', 'match', 'if', 'else', 'loop', 'while', 'for'],
            ProgrammingLanguage.C: ['include', 'define', 'typedef', 'struct', 'union', 'enum', 'static', 'extern', 'const', 'volatile'],
            ProgrammingLanguage.CSHARP: ['using', 'namespace', 'class', 'struct', 'interface', 'public', 'private', 'protected', 'static', 'virtual', 'override']
        }
    
    def detect_language(self, code: str) -> Tuple[ProgrammingLanguage, float]:
        """
        Detect programming language from code snippet
        Returns (language, confidence_score)
        """
        if not code or not code.strip():
            return ProgrammingLanguage.UNKNOWN, 0.0
        
        scores = {}
        
        # Score based on regex patterns
        for language, patterns in self.patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, code, re.MULTILINE | re.IGNORECASE))
                score += matches
            scores[language] = score
        
        # Additional scoring based on keywords
        words = re.findall(r'\b\w+\b', code.lower())
        for language, keywords in self.keywords.items():
            keyword_score = sum(1 for word in words if word in keywords)
            scores[language] = scores.get(language, 0) + keyword_score * 0.5
        
        # Find the language with highest score
        if not scores or max(scores.values()) == 0:
            return ProgrammingLanguage.UNKNOWN, 0.0
        
        best_language = max(scores, key=scores.get)
        max_score = scores[best_language]
        
        # Calculate confidence (normalize by code length and patterns)
        total_patterns = sum(len(patterns) for patterns in self.patterns.values())
        confidence = min(max_score / (len(code.split('\n')) + 1), 1.0)
        
        return best_language, confidence
    
    def get_language_info(self, language: ProgrammingLanguage) -> Dict[str, str]:
        """Get information about a programming language"""
        info = {
            ProgrammingLanguage.PYTHON: {
                "name": "Python",
                "extension": ".py",
                "comment": "#",
                "multiline_comment": '"""',
                "typical_patterns": ["def", "class", "import", "if __name__ == '__main__':"]
            },
            ProgrammingLanguage.JAVA: {
                "name": "Java",
                "extension": ".java",
                "comment": "//",
                "multiline_comment": "/* */",
                "typical_patterns": ["public class", "public static void main", "System.out.println"]
            },
            ProgrammingLanguage.CPP: {
                "name": "C++",
                "extension": ".cpp",
                "comment": "//",
                "multiline_comment": "/* */",
                "typical_patterns": ["#include", "using namespace std", "int main()"]
            },
            ProgrammingLanguage.JAVASCRIPT: {
                "name": "JavaScript",
                "extension": ".js",
                "comment": "//",
                "multiline_comment": "/* */",
                "typical_patterns": ["function", "const", "console.log", "=>"]
            },
            ProgrammingLanguage.GO: {
                "name": "Go",
                "extension": ".go",
                "comment": "//",
                "multiline_comment": "/* */",
                "typical_patterns": ["package main", "func main()", "fmt.Println"]
            },
            ProgrammingLanguage.RUST: {
                "name": "Rust",
                "extension": ".rs",
                "comment": "//",
                "multiline_comment": "/* */",
                "typical_patterns": ["fn main()", "let", "println!"]
            },
            ProgrammingLanguage.C: {
                "name": "C",
                "extension": ".c",
                "comment": "//",
                "multiline_comment": "/* */",
                "typical_patterns": ["#include", "int main()", "printf"]
            },
            ProgrammingLanguage.CSHARP: {
                "name": "C#",
                "extension": ".cs",
                "comment": "//",
                "multiline_comment": "/* */",
                "typical_patterns": ["using System", "public class", "static void Main"]
            }
        }
        
        return info.get(language, {
            "name": "Unknown",
            "extension": ".txt",
            "comment": "#",
            "multiline_comment": "",
            "typical_patterns": []
        })
    
    def suggest_improvements(self, code: str, language: ProgrammingLanguage) -> List[str]:
        """Suggest language-specific improvements"""
        suggestions = []
        
        if language == ProgrammingLanguage.PYTHON:
            if 'print(' in code and not any(word in code for word in ['logging', 'logger']):
                suggestions.append("Consider using logging instead of print statements for better debugging")
            if 'for i in range(len(' in code:
                suggestions.append("Consider using 'for item in list' instead of 'for i in range(len(list))'")
            if not re.search(r'def\s+\w+\s*\([^)]*\)\s*->', code) and 'def ' in code:
                suggestions.append("Consider adding type hints to function parameters and return values")
        
        elif language == ProgrammingLanguage.JAVA:
            if not re.search(r'@Override', code) and 'public ' in code:
                suggestions.append("Consider using @Override annotation when overriding methods")
            if 'System.out.print' in code:
                suggestions.append("Consider using a logging framework instead of System.out.print")
        
        elif language == ProgrammingLanguage.CPP:
            if 'using namespace std' in code:
                suggestions.append("Consider avoiding 'using namespace std' in header files")
            if 'malloc' in code and 'free' not in code:
                suggestions.append("Remember to free allocated memory or consider using smart pointers")
        
        elif language == ProgrammingLanguage.JAVASCRIPT:
            if 'var ' in code:
                suggestions.append("Consider using 'const' or 'let' instead of 'var'")
            if '==' in code and '===' not in code:
                suggestions.append("Consider using '===' for strict equality comparison")
        
        return suggestions


# Global language detector instance
language_detector = LanguageDetector()
