from fastapi import APIRouter, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from app.db.session_manager import session_manager
from app.models.qa_session import QASessionResponse, QAEntryResponse, QASessionWithEntries
from app.core.agent import dsa_agent
from app.core.memory_manager import memory_manager
from app.core.app_config import config_manager

router = APIRouter()
security = HTTPBearer(auto_error=False)


# Pydantic models for API requests/responses
class UserPreferences(BaseModel):
    preferred_language: str = "python"
    experience_level: str = "intermediate"
    gemini_api_key: Optional[str] = None
    learning_preferences: Optional[Dict[str, bool]] = None


class DSAQuestionRequest(BaseModel):
    question: str
    user_id: str = "default_user"


class HintRequest(BaseModel):
    hint_level: str = "approach"  # approach, algorithm, implementation, optimization
    user_id: str = "default_user"


class CodeReviewRequest(BaseModel):
    user_code: str
    user_id: str = "default_user"


class ConfigUpdateRequest(BaseModel):
    gemini_api_key: Optional[str] = None
    preferred_language: Optional[str] = None
    experience_level: Optional[str] = None


@router.get("/sessions", response_model=List[QASessionResponse])
async def get_sessions(limit: int = 10):
    """Get recent sessions"""
    try:
        sessions = session_manager.get_recent_sessions(limit)
        return sessions
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sessions/{session_id}", response_model=QASessionWithEntries)
async def get_session(session_id: int):
    """Get session with entries"""
    try:
        session = session_manager.get_session_with_entries(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        return session
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/current-entry", response_model=Optional[QAEntryResponse])
async def get_current_entry():
    """Get current entry"""
    try:
        entry = session_manager.get_current_entry()
        return entry
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# DSA-specific endpoints
@router.post("/dsa/store-question")
async def store_dsa_question(request: DSAQuestionRequest):
    """Store and analyze DSA question (Hotkey 1 functionality)"""
    try:
        # Set user context
        dsa_agent.current_user_id = request.user_id

        # Store and analyze question
        response = dsa_agent.store_dsa_question(request.question)

        return {
            "success": True,
            "response": response,
            "message": "DSA question stored and analyzed successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/dsa/get-hint")
async def get_dsa_hint(request: HintRequest):
    """Get progressive hint (Hotkey 2 functionality)"""
    try:
        # Set user context
        dsa_agent.current_user_id = request.user_id

        # Map hint level to enum
        from app.core.agent import DSAHintLevel
        hint_level_map = {
            "approach": DSAHintLevel.APPROACH,
            "algorithm": DSAHintLevel.ALGORITHM,
            "implementation": DSAHintLevel.IMPLEMENTATION,
            "optimization": DSAHintLevel.OPTIMIZATION
        }

        hint_level = hint_level_map.get(request.hint_level, DSAHintLevel.APPROACH)
        response = dsa_agent.get_progressive_hint(hint_level)

        return {
            "success": True,
            "response": response,
            "hint_level": request.hint_level,
            "message": "Hint generated successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/dsa/get-solution")
async def get_full_solution(user_id: str = "default_user"):
    """Get full solution (Hotkey 3 functionality)"""
    try:
        # Set user context
        dsa_agent.current_user_id = user_id

        response = dsa_agent.get_full_solution()

        return {
            "success": True,
            "response": response,
            "message": "Full solution generated successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/dsa/get-interview-solution")
async def get_interview_solution(user_id: str = "default_user"):
    """Get interview-ready solution (Hotkey 4 functionality)"""
    try:
        # Set user context
        dsa_agent.current_user_id = user_id

        response = dsa_agent.get_interview_ready_solution()

        return {
            "success": True,
            "response": response,
            "message": "Interview-ready solution generated successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/dsa/review-code")
async def review_user_code(request: CodeReviewRequest):
    """Review user's code (Hotkey 5 functionality)"""
    try:
        # Set user context
        dsa_agent.current_user_id = request.user_id

        response = dsa_agent.review_user_code(request.user_code)

        return {
            "success": True,
            "response": response,
            "message": "Code review completed successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# User configuration endpoints
@router.get("/user/preferences/{user_id}")
async def get_user_preferences(user_id: str):
    """Get user preferences and learning history"""
    try:
        profile = memory_manager.get_user_profile(user_id)

        # Don't expose sensitive data
        safe_profile = {
            "user_id": profile["user_id"],
            "preferred_language": profile["preferred_language"],
            "experience_level": profile["experience_level"],
            "learning_preferences": profile["learning_preferences"],
            "problem_categories": profile["problem_categories"],
            "hint_usage_patterns": profile["hint_usage_patterns"],
            "total_sessions": len(profile["session_history"])
        }

        return {
            "success": True,
            "profile": safe_profile
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/user/preferences/{user_id}")
async def update_user_preferences(user_id: str, preferences: UserPreferences):
    """Update user preferences"""
    try:
        # Update memory manager
        prefs_dict = {
            "preferred_language": preferences.preferred_language,
            "experience_level": preferences.experience_level
        }

        if preferences.learning_preferences:
            prefs_dict["learning_preferences"] = preferences.learning_preferences

        memory_manager.update_user_preferences(user_id, prefs_dict)

        # Update agent preferences
        dsa_agent.set_user_preferences(user_id, preferences.preferred_language)

        return {
            "success": True,
            "message": "User preferences updated successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/config/update")
async def update_config(config: ConfigUpdateRequest):
    """Update application configuration"""
    try:
        # Update Gemini API key if provided
        if config.gemini_api_key:
            # Update the config file or environment
            # For now, we'll update the agent directly
            try:
                from app.core.config import setting
                setting.GEMINI_API_KEY = config.gemini_api_key

                # Reinitialize the LLM
                dsa_agent._initialize_llm()

            except Exception as e:
                logger.warning(f"Could not update API key: {e}")

        # Update agent config
        if config.preferred_language:
            dsa_agent.preferred_language = config.preferred_language

        return {
            "success": True,
            "message": "Configuration updated successfully",
            "using_api": not dsa_agent.use_selenium_fallback
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config/status")
async def get_config_status():
    """Get current configuration status"""
    try:
        return {
            "success": True,
            "status": {
                "has_gemini_api": dsa_agent.llm is not None,
                "using_selenium_fallback": dsa_agent.use_selenium_fallback,
                "preferred_language": dsa_agent.preferred_language,
                "current_user": dsa_agent.current_user_id
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats/global")
async def get_global_stats():
    """Get global usage statistics"""
    try:
        if memory_manager.memory:
            stats = memory_manager.memory.get("global_stats", {})
        else:
            stats = {}

        return {
            "success": True,
            "stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
