import logging
from typing import Optional, Dict, Any, List
from enum import Enum

from langchain.schema import HumanMessage, SystemMessage
from langchain_google_genai import ChatGoogleGenerativeAI

from app.core.app_config import config_manager
from app.db.session_manager import session_manager
from app.models.qa_session import QAEntry
from app.core.memory_manager import memory_manager
from app.core.selenium_fallback import selenium_fallback
from app.utils.language_detector import language_detector, ProgrammingLanguage

logger = logging.getLogger(__name__)


class DSAHintLevel(Enum):
    """Levels of hints for DSA problems"""
    APPROACH = "approach"
    ALGORITHM = "algorithm"
    IMPLEMENTATION = "implementation"
    OPTIMIZATION = "optimization"


class DSAAgent:
    """DSA-focused AI agent with personalized assistance"""

    def __init__(self):
        self.config = config_manager.get_config()
        self.llm = None
        self.use_selenium_fallback = False
        self.current_user_id = "default_user"  # Will be configurable
        self.preferred_language = "python"  # Will be user configurable
        self._initialize_llm()
        self._initialize_memory()

    def _initialize_llm(self):
        """Initialize the language model with fallback to Selenium"""
        try:
            # Try to use Google Gemini first
            from app.core.config import setting

            if hasattr(setting, 'GEMINI_API_KEY') and setting.GEMINI_API_KEY:
                self.llm = ChatGoogleGenerativeAI(
                    model="gemini-pro",
                    google_api_key=setting.GEMINI_API_KEY,
                    temperature=self.config.agent.temperature,
                    max_output_tokens=self.config.agent.max_tokens
                )
                logger.info("Initialized Gemini LLM")
                self.use_selenium_fallback = False
            else:
                logger.warning("No Gemini API key found, will use Selenium fallback")
                self.llm = None
                self.use_selenium_fallback = True

        except Exception as e:
            logger.error(f"Failed to initialize LLM: {e}")
            logger.info("Falling back to Selenium ChatGPT automation")
            self.llm = None
            self.use_selenium_fallback = True

    def _initialize_memory(self):
        """Initialize mem0 for user personalization"""
        try:
            self.memory = memory_manager
            logger.info("Memory system initialized with DSA memory manager")
        except Exception as e:
            logger.error(f"Failed to initialize memory: {e}")
            self.memory = None
    
    def store_dsa_question(self, question: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Hotkey 1: Store DSA question and analyze it"""
        try:
            # Store question in session
            entry = session_manager.capture_question(question, context)

            # Analyze question type and difficulty
            analysis_prompt = f"""
            You are a DSA (Data Structures and Algorithms) expert assistant. Analyze this DSA question:

            Question: {question}

            Please provide:
            1. **Problem Type**: Identify the main DSA category (Array, String, Tree, Graph, DP, etc.)
            2. **Difficulty Level**: Easy/Medium/Hard
            3. **Key Concepts**: List the main algorithms/data structures needed
            4. **Similar Problems**: Mention 2-3 similar well-known problems
            5. **Initial Approach**: Brief high-level approach without giving away the solution

            Keep the response concise and educational. This is for a user learning DSA.
            """

            messages = [
                SystemMessage(content="You are an expert DSA tutor who helps students learn step by step."),
                HumanMessage(content=analysis_prompt)
            ]

            # Get response using API or Selenium fallback
            response_content = self._get_ai_response(
                messages,
                fallback_method="send_dsa_question",
                question=question
            )

            if not response_content:
                return "Error: Could not get AI response"

            # Store the analysis
            session_manager.save_agent_response(f"[QUESTION ANALYSIS]\n{response_content}", entry.id)

            # Record interaction in memory
            if self.memory:
                # Extract problem type from response (simplified)
                problem_type = "general"  # Could be enhanced with NLP to detect type
                difficulty = "medium"     # Could be enhanced with difficulty detection
                self.memory.record_question_interaction(self.current_user_id, question, problem_type, difficulty)

            return response_content

        except Exception as e:
            logger.error(f"Error storing DSA question: {e}")
            return f"Error analyzing question: {str(e)}"

    def get_progressive_hint(self, hint_level: DSAHintLevel = DSAHintLevel.APPROACH) -> str:
        """Hotkey 2: Get progressive hints without full solution"""
        if not self.llm:
            return "Error: AI agent not properly initialized"

        current_entry = session_manager.get_current_entry()
        if not current_entry or not current_entry.question:
            return "Error: No current DSA question found. Please store a question first using Hotkey 1."

        try:
            hint_prompts = {
                DSAHintLevel.APPROACH: """
                Provide a HINT about the general approach to solve this DSA problem.
                DO NOT give the complete solution or code.

                Focus on:
                - What data structure might be helpful?
                - What's the general strategy?
                - Any key insights about the problem?

                Keep it as a gentle nudge, not a full explanation.
                """,

                DSAHintLevel.ALGORITHM: """
                Provide a more detailed HINT about the algorithm to use.
                DO NOT give the complete solution or code.

                Focus on:
                - What specific algorithm or technique?
                - What are the main steps?
                - Any edge cases to consider?

                Still keep it as guidance, not a complete walkthrough.
                """,

                DSAHintLevel.IMPLEMENTATION: """
                Provide HINTS about implementation details.
                DO NOT write the complete code.

                Focus on:
                - Key functions or methods needed
                - Important variables to track
                - Loop structures or recursion patterns
                - Time/space complexity considerations

                Give implementation guidance without writing the full solution.
                """,

                DSAHintLevel.OPTIMIZATION: """
                Provide HINTS about optimizing the solution.

                Focus on:
                - Can we improve time complexity?
                - Can we reduce space usage?
                - Are there any mathematical optimizations?
                - Alternative approaches that might be better?

                Guide towards optimization without giving the complete optimized solution.
                """
            }

            prompt = f"""
            DSA Question: {current_entry.question}

            User's preferred programming language: {self.preferred_language}

            {hint_prompts[hint_level]}

            Remember: This is a HINT, not a solution. Help the user think through the problem.
            """

            messages = [
                SystemMessage(content="You are a patient DSA tutor who gives helpful hints without spoiling the learning experience."),
                HumanMessage(content=prompt)
            ]

            # Get response using API or Selenium fallback
            response_content = self._get_ai_response(
                messages,
                fallback_method="send_hint_request",
                question=current_entry.question,
                hint_level=hint_level.value
            )

            if not response_content:
                return "Error: Could not get AI response"

            # Store the hint
            session_manager.save_agent_response(f"[HINT - {hint_level.value.upper()}]\n{response_content}", current_entry.id)

            # Record hint request in memory
            if self.memory:
                self.memory.record_hint_request(self.current_user_id, hint_level.value)

            return response_content

        except Exception as e:
            logger.error(f"Error getting hint: {e}")
            return f"Error generating hint: {str(e)}"

    def get_full_solution(self) -> str:
        """Hotkey 3: Get full solution based on user history"""
        if not self.llm:
            return "Error: AI agent not properly initialized"

        current_entry = session_manager.get_current_entry()
        if not current_entry or not current_entry.question:
            return "Error: No current DSA question found. Please store a question first using Hotkey 1."

        try:
            # Get user's solving history and preferences from memory
            user_context = self._get_user_context()

            prompt = f"""
            DSA Question: {current_entry.question}

            User's preferred programming language: {self.preferred_language}
            User context: {user_context}

            Provide a COMPLETE solution with:

            1. **Problem Analysis**: Break down what the problem is asking
            2. **Approach**: Explain the chosen approach and why
            3. **Algorithm**: Step-by-step algorithm
            4. **Code Solution**: Complete working code in {self.preferred_language}
            5. **Complexity Analysis**: Time and space complexity
            6. **Test Cases**: 2-3 test cases with explanations
            7. **Alternative Approaches**: Mention other possible solutions

            Make the explanation educational and tailored to the user's experience level.
            """

            messages = [
                SystemMessage(content="You are an expert DSA instructor providing complete, educational solutions."),
                HumanMessage(content=prompt)
            ]

            # Get response using API or Selenium fallback
            response_content = self._get_ai_response(
                messages,
                fallback_method="send_solution_request",
                question=current_entry.question,
                solution_type="full"
            )

            if not response_content:
                return "Error: Could not get AI response"

            # Store the solution
            session_manager.save_agent_response(f"[FULL SOLUTION]\n{response_content}", current_entry.id)

            # Record solution request in memory
            if self.memory:
                self.memory.record_solution_request(self.current_user_id, "full_solution")

            return response_content

        except Exception as e:
            logger.error(f"Error generating full solution: {e}")
            return f"Error generating solution: {str(e)}"

    def get_interview_ready_solution(self) -> str:
        """Hotkey 4: Get interview-ready solution (brute force → optimized → tweaks)"""
        if not self.llm:
            return "Error: AI agent not properly initialized"

        current_entry = session_manager.get_current_entry()
        if not current_entry or not current_entry.question:
            return "Error: No current DSA question found. Please store a question first using Hotkey 1."

        try:
            prompt = f"""
            DSA Question: {current_entry.question}

            User's preferred programming language: {self.preferred_language}

            Provide an INTERVIEW-READY solution progression:

            ## 1. BRUTE FORCE APPROACH
            - Simple, straightforward solution
            - Code implementation
            - Time/Space complexity

            ## 2. OPTIMIZED APPROACH
            - Improved algorithm
            - Better time/space complexity
            - Code implementation
            - Explanation of optimization

            ## 3. INTERVIEW TWEAKS & TIPS
            - Edge cases to mention
            - Follow-up questions to expect
            - How to communicate your thought process
            - Common mistakes to avoid
            - What interviewers look for

            ## 4. VARIATIONS & EXTENSIONS
            - How this problem might be modified
            - Related problems to mention

            Format this as if you're coaching someone for a technical interview.
            """

            messages = [
                SystemMessage(content="You are a senior engineer coaching someone for technical interviews."),
                HumanMessage(content=prompt)
            ]

            # Get response using API or Selenium fallback
            response_content = self._get_ai_response(
                messages,
                fallback_method="send_solution_request",
                question=current_entry.question,
                solution_type="interview"
            )

            if not response_content:
                return "Error: Could not get AI response"

            # Store the interview solution
            session_manager.save_agent_response(f"[INTERVIEW SOLUTION]\n{response_content}", current_entry.id)

            # Record solution request in memory
            if self.memory:
                self.memory.record_solution_request(self.current_user_id, "interview_solution")

            return response_content

        except Exception as e:
            logger.error(f"Error generating interview solution: {e}")
            return f"Error generating interview solution: {str(e)}"

    def review_user_code(self, user_code: str) -> str:
        """Hotkey 5: Review user's code and provide fixes"""
        current_entry = session_manager.get_current_entry()
        if not current_entry or not current_entry.question:
            return "Error: No current DSA question found. Please store a question first using Hotkey 1."

        try:
            # Detect programming language from code
            detected_language, confidence = language_detector.detect_language(user_code)

            # Use detected language if confidence is high, otherwise use user preference
            if confidence > 0.3:
                review_language = detected_language.value
                language_info = language_detector.get_language_info(detected_language)
                language_suggestions = language_detector.suggest_improvements(user_code, detected_language)
            else:
                review_language = self.preferred_language
                language_info = {"name": self.preferred_language.title()}
                language_suggestions = []

            # Build language-specific suggestions
            lang_specific_notes = ""
            if language_suggestions:
                lang_specific_notes = f"\n\n## LANGUAGE-SPECIFIC SUGGESTIONS ({language_info['name']}):\n"
                for suggestion in language_suggestions:
                    lang_specific_notes += f"- {suggestion}\n"

            prompt = f"""
            DSA Question: {current_entry.question}

            User's Code Submission (Detected: {language_info['name']}, Confidence: {confidence:.2f}):
            ```{review_language}
            {user_code}
            ```

            Please provide a comprehensive code review:

            ## 1. CORRECTNESS ANALYSIS
            - Does the code solve the problem correctly?
            - Are there any logical errors?
            - Does it handle edge cases?
            - Test with sample inputs if possible

            ## 2. CODE QUALITY REVIEW
            - Code readability and style
            - Variable naming conventions
            - Code structure and organization
            - Comments and documentation

            ## 3. ALGORITHM EFFICIENCY
            - Time complexity analysis (Big O notation)
            - Space complexity analysis
            - Can the algorithm be optimized?
            - Alternative approaches with better complexity

            ## 4. SPECIFIC FIXES NEEDED
            - Point out exact lines that need changes
            - Provide corrected code snippets (NOT the entire rewritten solution)
            - Explain why each fix is needed
            - Show before/after comparisons for critical fixes

            ## 5. {language_info['name'].upper()} BEST PRACTICES
            - Language-specific improvements
            - Idiomatic {language_info['name']} patterns
            - Error handling suggestions
            - Memory management (if applicable)
            - Testing recommendations

            ## 6. LEARNING OPPORTUNITIES
            - What concepts does this code demonstrate well?
            - What areas need more practice?
            - Related problems to try next

            {lang_specific_notes}

            Focus on helping the user improve their existing code rather than rewriting it completely.
            Be encouraging and educational in your feedback.
            """

            messages = [
                SystemMessage(content=f"You are a senior software engineer and coding mentor providing constructive code review feedback. You specialize in {language_info['name']} and DSA problem solving."),
                HumanMessage(content=prompt)
            ]

            # Get response using API or Selenium fallback
            response_content = self._get_ai_response(
                messages,
                fallback_method="send_code_review",
                question=current_entry.question,
                user_code=user_code
            )

            if not response_content:
                return "Error: Could not get AI response"

            # Add language detection info to response
            detection_info = f"[LANGUAGE DETECTED: {language_info['name']} (confidence: {confidence:.2f})]\n\n"
            full_response = detection_info + response_content

            # Store the code review
            session_manager.save_agent_response(f"[CODE REVIEW]\n{full_response}", current_entry.id)

            return full_response

        except Exception as e:
            logger.error(f"Error reviewing code: {e}")
            return f"Error reviewing code: {str(e)}"

    def _get_ai_response(self, messages: List, fallback_method: str = None, **kwargs) -> Optional[str]:
        """Get AI response using either API or Selenium fallback"""
        if self.llm and not self.use_selenium_fallback:
            try:
                response = self.llm.invoke(messages)
                return response.content
            except Exception as e:
                logger.error(f"API call failed: {e}")
                logger.info("Falling back to Selenium")
                self.use_selenium_fallback = True

        # Use Selenium fallback
        if self.use_selenium_fallback and fallback_method:
            try:
                if not selenium_fallback.driver:
                    if not selenium_fallback.initialize():
                        return "Error: Could not initialize ChatGPT fallback. Please check your internet connection."

                if not selenium_fallback.is_logged_in:
                    if not selenium_fallback.check_login_status():
                        return "Error: Please log in to ChatGPT in the browser window that opened."

                # Call the appropriate fallback method
                method = getattr(selenium_fallback, fallback_method, None)
                if method:
                    return method(**kwargs)
                else:
                    return "Error: Fallback method not available."

            except Exception as e:
                logger.error(f"Selenium fallback failed: {e}")
                return f"Error: Both API and fallback failed. {str(e)}"

        return "Error: No AI service available. Please configure Gemini API key or ensure ChatGPT access."

    def _get_user_context(self) -> str:
        """Get user context for personalization using memory manager"""
        if self.memory:
            return self.memory.get_personalized_context(self.current_user_id)
        else:
            return f"User prefers {self.preferred_language}. Learning DSA concepts."

    def set_user_preferences(self, user_id: str, preferred_language: str) -> None:
        """Set user preferences"""
        self.current_user_id = user_id
        self.preferred_language = preferred_language.lower()

        # Update memory manager with new preferences
        if self.memory:
            self.memory.update_user_preferences(user_id, {
                "preferred_language": preferred_language.lower()
            })

        logger.info(f"Updated preferences for user {user_id}: language={preferred_language}")

    def get_supported_languages(self) -> List[str]:
        """Get list of supported programming languages"""
        return [lang.value for lang in ProgrammingLanguage if lang != ProgrammingLanguage.UNKNOWN]

    def get_language_specific_tips(self, language: str) -> List[str]:
        """Get language-specific DSA tips"""
        tips = {
            "python": [
                "Use list comprehensions for concise array operations",
                "Leverage built-in functions like sorted(), reversed(), enumerate()",
                "Use collections.defaultdict and collections.Counter for frequency problems",
                "Remember that strings are immutable - use list for character manipulation",
                "Use sets for O(1) lookup operations"
            ],
            "java": [
                "Use ArrayList for dynamic arrays, LinkedList for frequent insertions/deletions",
                "Leverage Collections.sort() and Arrays.sort() for sorting",
                "Use HashMap for O(1) key-value operations",
                "Remember to handle null checks and edge cases",
                "Use StringBuilder for string concatenation in loops"
            ],
            "cpp": [
                "Use vector for dynamic arrays, prefer it over arrays",
                "Leverage STL algorithms like sort(), binary_search(), lower_bound()",
                "Use unordered_map for O(1) hash operations, map for ordered operations",
                "Be careful with memory management and pointer arithmetic",
                "Use auto keyword for type inference with iterators"
            ],
            "javascript": [
                "Use Array methods like map(), filter(), reduce() for functional programming",
                "Leverage Set and Map for efficient lookups",
                "Use spread operator (...) for array operations",
                "Remember that arrays are objects - use Array.isArray() for type checking",
                "Use parseInt() and Number() for string to number conversion"
            ]
        }

        return tips.get(language.lower(), [
            "Focus on understanding the problem before coding",
            "Think about edge cases and constraints",
            "Consider time and space complexity",
            "Test your solution with sample inputs",
            "Practice explaining your approach clearly"
        ])

    def analyze_question_only(self, question: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Analyze a question and provide guidance"""
        if not self.llm:
            return "Error: AI agent not properly initialized"
        
        try:
            # Build context information
            context_info = ""
            if context:
                context_info = f"\nContext: The user was working in {context.get('app_name', 'unknown application')}"
                if context.get('title'):
                    context_info += f" with window title '{context['title']}'"
            
            human_prompt = f"""
Please analyze the following question and provide helpful guidance.

Question:
{question}
{context_info}

Please provide:
1. A clear understanding of what the question is asking
2. Key concepts or topics involved
3. A comprehensive answer or solution approach
4. Best practices and tips related to this topic
5. Common pitfalls to avoid

Format your response clearly with sections for each part.
"""
            
            messages = [
                SystemMessage(content=self.config.agent.system_prompt),
                HumanMessage(content=human_prompt)
            ]
            
            response = self.llm.invoke(messages)
            return response.content
            
        except Exception as e:
            logger.error(f"Error analyzing question: {e}")
            return f"Error analyzing question: {str(e)}"
    
    def analyze_current_entry(self) -> Optional[str]:
        """Analyze the current Q&A entry"""
        current_entry = session_manager.get_current_entry()
        
        if not current_entry:
            logger.warning("No current entry to analyze")
            return None
        
        if not current_entry.question:
            logger.warning("Current entry has no question")
            return None
        
        # Parse context if available
        context = None
        if current_entry.question_context:
            try:
                import json
                context = json.loads(current_entry.question_context)
            except Exception as e:
                logger.error(f"Error parsing question context: {e}")
        
        # Analyze based on what we have
        if current_entry.solution:
            # We have both question and solution
            response = self.analyze_question_solution(
                current_entry.question,
                current_entry.solution,
                context
            )
        else:
            # We only have a question
            response = self.analyze_question_only(
                current_entry.question,
                context
            )
        
        # Save the response
        session_manager.save_agent_response(response, current_entry.id)
        
        logger.info(f"Analyzed entry {current_entry.id}")
        return response
    
    def get_analysis_summary(self, entry: QAEntry) -> Dict[str, Any]:
        """Get a summary of an analysis"""
        return {
            "entry_id": entry.id,
            "has_question": bool(entry.question),
            "has_solution": bool(entry.solution),
            "has_agent_response": bool(entry.agent_response),
            "question_length": len(entry.question) if entry.question else 0,
            "solution_length": len(entry.solution) if entry.solution else 0,
            "response_length": len(entry.agent_response) if entry.agent_response else 0,
            "created_at": entry.created_at,
            "last_updated": entry.updated_at
        }


# Global agent instance
dsa_agent = DSAAgent()

# Backward compatibility alias
qa_agent = dsa_agent
