// DSA Assistant Frontend JavaScript

const API_BASE = '/api/v1';
let currentUserId = 'default_user';

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    checkStatus();
    loadUserPreferences();
    loadStats();
    setupEventListeners();
});

function setupEventListeners() {
    // Toggle API key visibility
    document.getElementById('toggleApiKey').addEventListener('click', function() {
        const apiKeyInput = document.getElementById('geminiApiKey');
        const icon = this.querySelector('i');
        
        if (apiKeyInput.type === 'password') {
            apiKeyInput.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            apiKeyInput.type = 'password';
            icon.className = 'fas fa-eye';
        }
    });

    // Save configuration
    document.getElementById('saveConfig').addEventListener('click', saveConfiguration);
}

async function checkStatus() {
    try {
        const response = await fetch(`${API_BASE}/config/status`);
        const data = await response.json();
        
        if (data.success) {
            updateStatusIndicator(data.status);
        }
    } catch (error) {
        console.error('Error checking status:', error);
        updateStatusIndicator({ has_gemini_api: false, using_selenium_fallback: false });
    }
}

function updateStatusIndicator(status) {
    const indicator = document.querySelector('.status-indicator');
    const statusText = document.getElementById('status-text');
    
    if (status.has_gemini_api && !status.using_selenium_fallback) {
        indicator.className = 'status-indicator status-online';
        statusText.textContent = 'Gemini API Connected';
    } else if (status.using_selenium_fallback) {
        indicator.className = 'status-indicator status-fallback';
        statusText.textContent = 'Using ChatGPT Fallback';
    } else {
        indicator.className = 'status-indicator status-offline';
        statusText.textContent = 'No AI Service';
    }
}

async function loadUserPreferences() {
    try {
        const response = await fetch(`${API_BASE}/user/preferences/${currentUserId}`);
        const data = await response.json();
        
        if (data.success) {
            const profile = data.profile;
            document.getElementById('preferredLanguage').value = profile.preferred_language || 'python';
            document.getElementById('experienceLevel').value = profile.experience_level || 'intermediate';
        }
    } catch (error) {
        console.error('Error loading user preferences:', error);
    }
}

async function saveConfiguration() {
    const apiKey = document.getElementById('geminiApiKey').value;
    const preferredLanguage = document.getElementById('preferredLanguage').value;
    const experienceLevel = document.getElementById('experienceLevel').value;
    
    try {
        // Update configuration
        const configResponse = await fetch(`${API_BASE}/config/update`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                gemini_api_key: apiKey || null,
                preferred_language: preferredLanguage,
                experience_level: experienceLevel
            })
        });
        
        // Update user preferences
        const prefsResponse = await fetch(`${API_BASE}/user/preferences/${currentUserId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                preferred_language: preferredLanguage,
                experience_level: experienceLevel
            })
        });
        
        if (configResponse.ok && prefsResponse.ok) {
            showAlert('Configuration saved successfully!', 'success');
            checkStatus(); // Refresh status
        } else {
            showAlert('Error saving configuration', 'danger');
        }
    } catch (error) {
        console.error('Error saving configuration:', error);
        showAlert('Error saving configuration', 'danger');
    }
}

async function storeQuestion() {
    const question = document.getElementById('questionInput').value.trim();
    
    if (!question) {
        showAlert('Please enter a DSA question', 'warning');
        return;
    }
    
    showLoading('Analyzing question...');
    
    try {
        const response = await fetch(`${API_BASE}/dsa/store-question`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                question: question,
                user_id: currentUserId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayResponse(data.response, 'Question Analysis');
            document.getElementById('questionInput').value = '';
            loadStats();
        } else {
            showAlert('Error analyzing question', 'danger');
        }
    } catch (error) {
        console.error('Error storing question:', error);
        showAlert('Error analyzing question', 'danger');
    }
}

async function getHint() {
    const hintLevel = document.getElementById('hintLevel').value;
    
    showLoading('Generating hint...');
    
    try {
        const response = await fetch(`${API_BASE}/dsa/get-hint`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                hint_level: hintLevel,
                user_id: currentUserId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayResponse(data.response, `${hintLevel.charAt(0).toUpperCase() + hintLevel.slice(1)} Hint`);
            loadStats();
        } else {
            showAlert('Error generating hint', 'danger');
        }
    } catch (error) {
        console.error('Error getting hint:', error);
        showAlert('Error generating hint', 'danger');
    }
}

async function getFullSolution() {
    showLoading('Generating full solution...');
    
    try {
        const response = await fetch(`${API_BASE}/dsa/get-solution`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: currentUserId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayResponse(data.response, 'Full Solution');
            loadStats();
        } else {
            showAlert('Error generating solution', 'danger');
        }
    } catch (error) {
        console.error('Error getting solution:', error);
        showAlert('Error generating solution', 'danger');
    }
}

async function getInterviewSolution() {
    showLoading('Generating interview-ready solution...');
    
    try {
        const response = await fetch(`${API_BASE}/dsa/get-interview-solution`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: currentUserId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayResponse(data.response, 'Interview-Ready Solution');
            loadStats();
        } else {
            showAlert('Error generating interview solution', 'danger');
        }
    } catch (error) {
        console.error('Error getting interview solution:', error);
        showAlert('Error generating interview solution', 'danger');
    }
}

async function reviewCode() {
    const code = document.getElementById('codeInput').value.trim();
    
    if (!code) {
        showAlert('Please enter your code for review', 'warning');
        return;
    }
    
    showLoading('Reviewing code...');
    
    try {
        const response = await fetch(`${API_BASE}/dsa/review-code`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_code: code,
                user_id: currentUserId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayResponse(data.response, 'Code Review');
            document.getElementById('codeInput').value = '';
            loadStats();
        } else {
            showAlert('Error reviewing code', 'danger');
        }
    } catch (error) {
        console.error('Error reviewing code:', error);
        showAlert('Error reviewing code', 'danger');
    }
}

async function loadStats() {
    try {
        const response = await fetch(`${API_BASE}/stats/global`);
        const data = await response.json();
        
        if (data.success && data.stats) {
            document.getElementById('totalQuestions').textContent = data.stats.total_questions || 0;
            document.getElementById('totalHints').textContent = data.stats.total_hints_requested || 0;
            document.getElementById('totalSolutions').textContent = data.stats.total_solutions_generated || 0;
            document.getElementById('currentStreak').textContent = '🔥'; // Placeholder
        }
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

function displayResponse(response, title) {
    const responseArea = document.getElementById('responseArea');
    const timestamp = new Date().toLocaleTimeString();
    
    responseArea.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="mb-0"><i class="fas fa-robot"></i> ${title}</h6>
            <small class="text-muted">${timestamp}</small>
        </div>
        <div class="border-top pt-2">
            <pre class="mb-0" style="white-space: pre-wrap; font-family: inherit;">${response}</pre>
        </div>
    `;
    
    // Scroll to response
    responseArea.scrollIntoView({ behavior: 'smooth' });
}

function showLoading(message) {
    const responseArea = document.getElementById('responseArea');
    responseArea.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">${message}</p>
        </div>
    `;
}

function clearResponse() {
    const responseArea = document.getElementById('responseArea');
    responseArea.innerHTML = `
        <div class="text-muted text-center">
            <i class="fas fa-comment-dots fa-2x mb-2"></i>
            <p>AI responses will appear here...</p>
        </div>
    `;
}

function showAlert(message, type) {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
