[tool.poetry]
name = "agent"
version = "0.1.0"
description = ""
authors = ["kartikjn <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
langgraph = "^0.4.10"
langsmith = "^0.4.2"
fastapi = {extras = ["standard"], version = "^0.116.1"}
pydantic = "^2.11.7"
langchain = {extras = ["google-genai"], version = "^0.3.26"}
sqlalchemy = "^2.0.41"
pydantic-settings = "^2.10.1"
psycopg2-binary = "^2.9.10"
pytest = "^8.4.1"
jinja2 = "^3.1.6"
mem0ai = "^0.1.114"
psutil = "^7.0.0"
pystray = "^0.19.5"
pyyaml = "^6.0.2"
pillow = "^11.3.0"
selenium = "^4.34.2"
webdriver-manager = "^4.0.2"
uvicorn = "^0.35.0"
plyer = "^2.1.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
