#!/usr/bin/env python3
"""
Launch script for DSA Assistant Agent
"""

import sys
import os
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import fastapi
        import pydantic
        import sqlalchemy
        import langchain
        return True
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("Please run 'python setup.py' to install dependencies")
        return False

def show_help():
    """Show help information"""
    print("""
🧠 DSA Assistant Agent - Launch Script

Usage: python launch.py [mode] [options]

Modes:
  api     - Start web interface (default)
  tray    - Start system tray application
  system  - Start background service
  test    - Run test suite
  setup   - Run setup script

Options:
  --help, -h    - Show this help
  --port PORT   - Specify port for API mode (default: 8000)

Examples:
  python launch.py                    # Start web interface
  python launch.py api --port 3000    # Start web interface on port 3000
  python launch.py tray               # Start system tray
  python launch.py test               # Run tests
  python launch.py setup              # Run setup

🌐 Web interface: http://localhost:8000
📚 API docs: http://localhost:8000/docs
⌨️  Hotkeys: Ctrl+Shift+1-5 for DSA functions
""")

def main():
    """Main launch function"""
    args = sys.argv[1:]

    # Handle help
    if not args or args[0] in ['--help', '-h', 'help']:
        show_help()
        return 0

    mode = args[0]

    # Handle special modes
    if mode == 'setup':
        print("🚀 Running setup...")
        return subprocess.call([sys.executable, 'setup.py'])

    elif mode == 'test':
        print("🧪 Running test suite...")
        return subprocess.call([sys.executable, 'test_dsa_agent.py'])

    # Check dependencies for main modes
    if not check_dependencies():
        return 1

    # Add the app directory to the Python path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

    # Import and run main application
    try:
        from app.main import main as app_main

        # Handle port option for API mode
        if mode == 'api' and '--port' in args:
            try:
                port_index = args.index('--port') + 1
                port = int(args[port_index])
                os.environ['PORT'] = str(port)
            except (IndexError, ValueError):
                print("❌ Invalid port specified")
                return 1

        # Set mode and run
        sys.argv = ['launch.py', mode]
        return app_main()

    except ImportError as e:
        print(f"❌ Failed to import application: {e}")
        print("Please run 'python launch.py setup' first")
        return 1
    except KeyboardInterrupt:
        print("\n👋 DSA Assistant Agent stopped")
        return 0
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
