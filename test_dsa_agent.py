#!/usr/bin/env python3
"""
Comprehensive test script for DSA Assistant Agent
Tests all major functionality without requiring external dependencies
"""

import sys
import os
import time
import json
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent / "app"))

def test_imports():
    """Test that all modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        from app.core.agent import DSAAgent, dsa_agent
        from app.core.memory_manager import memory_manager
        from app.utils.language_detector import language_detector, ProgrammingLanguage
        from app.core.app_config import config_manager
        from app.db.session_manager import session_manager
        print("✅ All core modules imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_language_detection():
    """Test language detection functionality"""
    print("\n🔍 Testing language detection...")
    
    try:
        from app.utils.language_detector import language_detector, ProgrammingLanguage
        
        # Test Python code
        python_code = """
def two_sum(nums, target):
    hash_map = {}
    for i, num in enumerate(nums):
        complement = target - num
        if complement in hash_map:
            return [hash_map[complement], i]
        hash_map[num] = i
    return []
"""
        
        lang, confidence = language_detector.detect_language(python_code)
        print(f"Python detection: {lang.value} (confidence: {confidence:.2f})")
        
        # Test Java code
        java_code = """
public class Solution {
    public int[] twoSum(int[] nums, int target) {
        Map<Integer, Integer> map = new HashMap<>();
        for (int i = 0; i < nums.length; i++) {
            int complement = target - nums[i];
            if (map.containsKey(complement)) {
                return new int[] { map.get(complement), i };
            }
            map.put(nums[i], i);
        }
        return new int[0];
    }
}
"""
        
        lang, confidence = language_detector.detect_language(java_code)
        print(f"Java detection: {lang.value} (confidence: {confidence:.2f})")
        
        print("✅ Language detection working")
        return True
    except Exception as e:
        print(f"❌ Language detection error: {e}")
        return False

def test_memory_manager():
    """Test memory manager functionality"""
    print("\n🔍 Testing memory manager...")
    
    try:
        from app.core.memory_manager import memory_manager
        
        # Test user profile creation
        profile = memory_manager.get_user_profile("test_user")
        print(f"Created user profile: {profile['user_id']}")
        
        # Test preference updates
        memory_manager.update_user_preferences("test_user", {
            "preferred_language": "python",
            "experience_level": "intermediate"
        })
        
        # Test interaction recording
        memory_manager.record_question_interaction("test_user", "Two Sum Problem", "array", "easy")
        memory_manager.record_hint_request("test_user", "approach")
        memory_manager.record_solution_request("test_user", "full_solution")
        
        # Test personalized context
        context = memory_manager.get_personalized_context("test_user")
        print(f"Personalized context: {context}")
        
        print("✅ Memory manager working")
        return True
    except Exception as e:
        print(f"❌ Memory manager error: {e}")
        return False

def test_dsa_agent():
    """Test DSA agent functionality"""
    print("\n🔍 Testing DSA agent...")
    
    try:
        from app.core.agent import dsa_agent
        
        # Test agent initialization
        print(f"Agent initialized: {dsa_agent is not None}")
        print(f"Using fallback: {dsa_agent.use_selenium_fallback}")
        print(f"Preferred language: {dsa_agent.preferred_language}")
        
        # Test user preferences
        dsa_agent.set_user_preferences("test_user", "python")
        
        # Test language support
        languages = dsa_agent.get_supported_languages()
        print(f"Supported languages: {languages}")
        
        # Test language tips
        tips = dsa_agent.get_language_specific_tips("python")
        print(f"Python tips: {len(tips)} tips available")
        
        print("✅ DSA agent basic functionality working")
        return True
    except Exception as e:
        print(f"❌ DSA agent error: {e}")
        return False

def test_database():
    """Test database functionality"""
    print("\n🔍 Testing database...")
    
    try:
        from app.db.session_manager import session_manager
        
        # Test session creation
        session = session_manager.create_session()
        print(f"Created session: {session.id}")
        
        # Test entry creation
        entry = session_manager.create_entry()
        print(f"Created entry: {entry.id}")
        
        # Test question capture
        test_question = "Given an array of integers, return indices of two numbers that add up to target."
        entry = session_manager.capture_question(test_question)
        print(f"Captured question: {len(test_question)} characters")
        
        # Test solution capture
        test_solution = "Use hash map to store complements"
        entry = session_manager.capture_solution(test_solution)
        print(f"Captured solution: {len(test_solution)} characters")
        
        # Test agent response saving
        test_response = "This is a classic Two Sum problem..."
        session_manager.save_agent_response(test_response, entry.id)
        print(f"Saved agent response: {len(test_response)} characters")
        
        print("✅ Database functionality working")
        return True
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_configuration():
    """Test configuration management"""
    print("\n🔍 Testing configuration...")
    
    try:
        from app.core.app_config import config_manager
        
        # Test config loading
        config = config_manager.get_config()
        print(f"Config loaded: {config is not None}")
        print(f"Hotkeys configured: {len(config.hotkeys)}")
        print(f"Agent model: {config.agent.model}")
        print(f"Preferred language: {config.agent.preferred_language}")
        
        # Test hotkey configuration
        for hotkey_id, hotkey_config in config.hotkeys.items():
            print(f"  {hotkey_id}: {'+'.join(hotkey_config.keys)} -> {hotkey_config.action}")
        
        print("✅ Configuration working")
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_api_endpoints():
    """Test API endpoint definitions"""
    print("\n🔍 Testing API endpoints...")
    
    try:
        from app.api import router
        
        # Count routes
        route_count = len(router.routes)
        print(f"API routes defined: {route_count}")
        
        # List some key routes
        for route in router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                methods = list(route.methods) if route.methods else ['GET']
                print(f"  {methods[0]} {route.path}")
        
        print("✅ API endpoints defined")
        return True
    except Exception as e:
        print(f"❌ API endpoints error: {e}")
        return False

def test_frontend_files():
    """Test frontend file existence"""
    print("\n🔍 Testing frontend files...")
    
    try:
        # Check template files
        template_path = Path("app/templates/index.html")
        if template_path.exists():
            print(f"✅ Template file exists: {template_path}")
        else:
            print(f"❌ Template file missing: {template_path}")
            return False
        
        # Check static files
        static_path = Path("app/static/app.js")
        if static_path.exists():
            print(f"✅ Static file exists: {static_path}")
        else:
            print(f"❌ Static file missing: {static_path}")
            return False
        
        print("✅ Frontend files present")
        return True
    except Exception as e:
        print(f"❌ Frontend files error: {e}")
        return False

def run_integration_test():
    """Run a complete integration test"""
    print("\n🔍 Running integration test...")
    
    try:
        from app.core.agent import dsa_agent
        from app.db.session_manager import session_manager
        
        # Simulate the complete workflow
        print("1. Storing a DSA question...")
        test_question = """
        Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.
        You may assume that each input would have exactly one solution, and you may not use the same element twice.
        """
        
        # This would normally call the AI, but we'll simulate it
        print("   Question stored (simulated)")
        
        print("2. Getting a hint...")
        # This would normally call the AI for a hint
        print("   Hint generated (simulated)")
        
        print("3. Reviewing code...")
        test_code = """
def twoSum(nums, target):
    for i in range(len(nums)):
        for j in range(i+1, len(nums)):
            if nums[i] + nums[j] == target:
                return [i, j]
    return []
"""
        
        # Test language detection on the code
        from app.utils.language_detector import language_detector
        lang, confidence = language_detector.detect_language(test_code)
        print(f"   Code language detected: {lang.value} (confidence: {confidence:.2f})")
        
        print("✅ Integration test completed successfully")
        return True
    except Exception as e:
        print(f"❌ Integration test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 DSA Assistant Agent - Comprehensive Test Suite")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Language Detection", test_language_detection),
        ("Memory Manager", test_memory_manager),
        ("DSA Agent", test_dsa_agent),
        ("Database", test_database),
        ("Configuration", test_configuration),
        ("API Endpoints", test_api_endpoints),
        ("Frontend Files", test_frontend_files),
        ("Integration", run_integration_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! DSA Assistant Agent is ready to use.")
        print("\n📋 Next steps:")
        print("1. Run 'python -m app.main api' to start the web interface")
        print("2. Open http://localhost:8000 in your browser")
        print("3. Configure your API key and preferences")
        print("4. Test the hotkey functions")
    else:
        print(f"⚠️  {total - passed} tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
