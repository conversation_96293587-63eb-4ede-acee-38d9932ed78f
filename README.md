# 🧠 DSA Assistant Agent

An AI-powered Data Structures and Algorithms learning assistant that helps you master DSA concepts through personalized hints, solutions, and code reviews.

## ✨ Features

### 🎯 5 Hotkey Functions
- **Hotkey 1 (<PERSON>trl+<PERSON>ft+1)**: Copy and store DSA questions with instant AI analysis
- **Hotkey 2 (<PERSON><PERSON><PERSON>+<PERSON>ft+2)**: Get progressive hints without spoiling the solution
- **Hotkey 3 (Ctrl+Shift+3)**: Get complete solutions based on your learning history
- **Hotkey 4 (Ctrl+Shift+4)**: Get interview-ready solutions (brute force → optimized)
- **Hotkey 5 (<PERSON>trl+Shift+5)**: Review your code and get improvement suggestions

### 🤖 AI Integration
- **Primary**: Google Gemini API for fast, accurate responses
- **Fallback**: Automated Selenium + ChatGPT web interface when API key not configured
- **Smart Switching**: Automatically falls back if API fails

### 👤 Personalization (via mem0)
- Tracks your solved problems and struggled topics
- Remembers your preferred programming language
- Adapts hints and solutions to your experience level
- Learns from your hint usage patterns

### 🌐 Multi-Interface
- **Web Interface**: Modern, responsive frontend for configuration and testing
- **System Tray**: Background service with hotkey support
- **API**: RESTful API for integration with other tools

### 💻 Language Support
- Python, Java, C++, JavaScript, Go, Rust
- All solutions and hints respect your language preference
- Code reviews tailored to language-specific best practices

## 🚀 Quick Start

### 1. Installation
```bash
# Clone the repository
git clone <repository-url>
cd dsa-assistant-agent

# Run the setup script
python setup.py
```

### 2. Configuration
Edit the `.env` file to add your Gemini API key (optional):
```env
GEMINI_API_KEY=your_gemini_api_key_here
```

**Note**: If you don't have a Gemini API key, the system will automatically use ChatGPT web interface as fallback.

### 3. Run the Application

#### Web Interface (Recommended for first-time users)
```bash
python -m app.main api
```
Then open http://localhost:8000 in your browser.

#### System Tray (For daily use)
```bash
python -m app.main tray
```

#### Background Service (For hotkey-only usage)
```bash
python -m app.main system
```

## 📖 How to Use

### Web Interface
1. Open http://localhost:8000
2. Configure your API key and preferences
3. Test the 5 hotkey functions directly in the browser
4. View your learning statistics and history

### Hotkey Usage
1. **Store Question**: Select any DSA question text, press Ctrl+Shift+1
2. **Get Hint**: Press Ctrl+Shift+2 for a helpful hint
3. **Get Solution**: Press Ctrl+Shift+3 for complete solution
4. **Interview Prep**: Press Ctrl+Shift+4 for interview-ready progression
5. **Code Review**: Select your code, press Ctrl+Shift+5 for review

### Example Workflow
1. Copy a LeetCode problem → Ctrl+Shift+1 (stores and analyzes)
2. Try to solve it yourself
3. Stuck? → Ctrl+Shift+2 (get a hint)
4. Still stuck? → Ctrl+Shift+2 again (more detailed hint)
5. Need full solution? → Ctrl+Shift+3
6. Preparing for interview? → Ctrl+Shift+4 (brute force → optimized)
7. Want code review? → Select your code → Ctrl+Shift+5

## 🛠️ Architecture

```
DSA Assistant Agent
├── 🎯 Core Agent (app/core/agent.py)
│   ├── DSA-specific prompts and logic
│   ├── Gemini API integration
│   └── Selenium fallback system
├── 🧠 Memory Manager (app/core/memory_manager.py)
│   ├── User personalization with mem0
│   ├── Learning history tracking
│   └── Preference management
├── ⌨️ Hotkey System (app/utils/hotkey_listener.py)
│   ├── Cross-platform hotkey detection
│   └── 5 DSA-specific actions
├── 🌐 Web Frontend (app/templates/ + app/static/)
│   ├── Configuration interface
│   ├── Testing playground
│   └── Statistics dashboard
├── 🔌 API Layer (app/api.py)
│   ├── RESTful endpoints
│   └── User management
└── 🗄️ Database (SQLite/PostgreSQL)
    ├── Session management
    ├── Q&A history
    └── User preferences
```

## ⚙️ Configuration

The application creates configuration files in `~/.qa_agent/`:

### Default Hotkeys
- **Ctrl+Shift+1**: Store DSA question
- **Ctrl+Shift+2**: Get progressive hint
- **Ctrl+Shift+3**: Get full solution
- **Ctrl+Shift+4**: Get interview solution
- **Ctrl+Shift+5**: Review code

### Environment Variables (.env)
```env
# Optional - will use ChatGPT fallback if not provided
GEMINI_API_KEY=your_gemini_api_key_here

# Database (SQLite by default)
DB_HOST=localhost
DB_PORT=5432
DB_PASSWORD=password
DB_NAME=dsa_agent
DB_USER=user
```

## Data Storage

The application stores data locally in:
- **Configuration**: `~/.qa_agent/config.yaml`
- **Database**: `qa_agent.db` (SQLite)
- **Logs**: `qa_agent.log`

## System Tray Features

When running in tray mode, you can:
- Start/stop the hotkey service
- View current Q&A entry
- View recent sessions
- Access settings
- View application information

## Troubleshooting

### Common Issues

1. **Hotkeys not working**:
   - Ensure the application is running with proper permissions
   - Check if other applications are using the same hotkey combinations
   - Try different hotkey combinations in the configuration

2. **Text not being captured**:
   - Make sure text is selected before pressing hotkeys
   - Check clipboard permissions on your system
   - Verify that clipboard utilities are installed (Linux)

3. **AI analysis not working**:
   - Verify your GEMINI_API_KEY is set correctly
   - Check internet connection
   - Review logs for error messages

4. **System tray not appearing**:
   - Install required dependencies: `pip install pystray pillow`
   - Use fallback GUI mode if tray is not supported

### Platform-Specific Notes

**macOS:**
- May require accessibility permissions for global hotkeys
- Go to System Preferences > Security & Privacy > Accessibility

**Windows:**
- May require running as administrator for global hotkeys
- Windows Defender might flag the application initially

**Linux:**
- Requires X11 (not Wayland) for hotkey detection
- Install xdotool and xclip/xsel packages

## Development

### Project Structure

```
app/
├── core/           # Core configuration and agent logic
├── db/             # Database models and session management
├── models/         # Pydantic models
├── utils/          # Utility modules (hotkeys, clipboard, window detection)
├── main.py         # Main entry point
├── system_app.py   # System application logic
├── tray_app.py     # System tray application
└── api.py          # API endpoints
```

### Running Tests

```bash
poetry run pytest
```

### Adding New Features

1. Update configuration models in `app/core/app_config.py`
2. Add new hotkey actions in `app/system_app.py`
3. Update the system tray menu in `app/tray_app.py`
4. Add API endpoints in `app/api.py` if needed

## License

This project is licensed under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs in `qa_agent.log`
3. Create an issue on GitHub with detailed information
