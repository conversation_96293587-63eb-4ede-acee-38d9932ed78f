#!/usr/bin/env python3
"""
Setup script for DSA Assistant Agent
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def install_dependencies():
    """Install Python dependencies"""
    print("\n📦 Installing Python dependencies...")
    
    # Install core dependencies first
    core_deps = [
        "fastapi[standard]>=0.116.1",
        "pydantic>=2.11.7",
        "pydantic-settings>=2.10.1",
        "sqlalchemy>=2.0.41",
        "jinja2>=3.1.0",
        "uvicorn>=0.30.0",
        "pyyaml>=6.0.2"
    ]
    
    for dep in core_deps:
        if not run_command(f"pip install '{dep}'", f"Installing {dep.split('>=')[0]}"):
            return False
    
    # Install AI dependencies
    ai_deps = [
        "langchain[google-genai]>=0.3.26",
        "langgraph>=0.4.10",
        "langsmith>=0.4.2"
    ]
    
    for dep in ai_deps:
        if not run_command(f"pip install '{dep}'", f"Installing {dep.split('>=')[0]}"):
            print(f"⚠️  Warning: Failed to install {dep}. AI features may not work.")
    
    # Install system dependencies (optional)
    system_deps = [
        "pynput>=1.8.1",
        "pyperclip>=1.9.0",
        "psutil>=7.0.0",
        "pystray>=0.19.5",
        "pillow>=11.3.0"
    ]
    
    for dep in system_deps:
        if not run_command(f"pip install '{dep}'", f"Installing {dep.split('>=')[0]}"):
            print(f"⚠️  Warning: Failed to install {dep}. Hotkey features may not work.")
    
    # Install Selenium dependencies (optional)
    selenium_deps = [
        "selenium>=4.15.0",
        "webdriver-manager>=4.0.0"
    ]
    
    for dep in selenium_deps:
        if not run_command(f"pip install '{dep}'", f"Installing {dep.split('>=')[0]}"):
            print(f"⚠️  Warning: Failed to install {dep}. Selenium fallback may not work.")
    
    # Try to install mem0 (optional)
    if not run_command("pip install mem0ai>=0.1.0", "Installing mem0ai"):
        print("⚠️  Warning: Failed to install mem0ai. Using basic memory system.")
    
    return True

def create_config_files():
    """Create necessary configuration files"""
    print("\n⚙️  Creating configuration files...")
    
    # Create .env file if it doesn't exist
    env_file = Path(".env")
    if not env_file.exists():
        env_content = """# DSA Assistant Agent Configuration
# Add your Gemini API key here (optional - will use ChatGPT fallback if not provided)
GEMINI_API_KEY=your_gemini_api_key_here

# Database configuration (using SQLite by default)
DB_HOST=localhost
DB_PORT=5432
DB_PASSWORD=password
DB_NAME=dsa_agent
DB_USER=user

# You can leave these as-is for SQLite
"""
        with open(env_file, 'w') as f:
            f.write(env_content)
        print("✅ Created .env configuration file")
    else:
        print("✅ .env file already exists")
    
    # Create data directory
    data_dir = Path.home() / ".qa_agent"
    data_dir.mkdir(exist_ok=True)
    print(f"✅ Created data directory: {data_dir}")
    
    return True

def setup_database():
    """Initialize the database"""
    print("\n🗄️  Setting up database...")
    try:
        # Import and initialize database
        from app.db.session_manager import session_manager
        print("✅ Database initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 DSA Assistant Agent Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Dependency installation failed. Please check the errors above.")
        sys.exit(1)
    
    # Create config files
    if not create_config_files():
        print("\n❌ Configuration setup failed.")
        sys.exit(1)
    
    # Setup database
    if not setup_database():
        print("\n⚠️  Database setup failed, but you can still use the application.")
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file to add your Gemini API key (optional)")
    print("2. Run the application:")
    print("   • Web interface: python -m app.main api")
    print("   • System tray: python -m app.main tray")
    print("   • Background service: python -m app.main system")
    print("\n🌐 Web interface will be available at: http://localhost:8000")
    print("📚 API documentation at: http://localhost:8000/docs")
    
    # Show hotkey information
    print("\n⌨️  Default Hotkeys:")
    print("   • Ctrl+Shift+1: Store DSA question")
    print("   • Ctrl+Shift+2: Get hint")
    print("   • Ctrl+Shift+3: Get full solution")
    print("   • Ctrl+Shift+4: Get interview solution")
    print("   • Ctrl+Shift+5: Review code")

if __name__ == "__main__":
    main()
